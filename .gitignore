# MacOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
go.work.sum

# Build artifacts
/bin/
/build/
/dist/
/target/
*.o
*.a

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.cover
*.prof

# Dependency directories
vendor/
node_modules/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# OS generated files
.fseventsd
.Spotlight-V100
.TemporaryItems
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Environment variables
.env
.env.local
.env.*.local
